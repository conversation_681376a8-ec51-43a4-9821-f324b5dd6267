version: "3"
agent:
    authtoken: 2sEzSQeNl3wS1omP2rA4Ely4NLD_4jpVVGAe2fqhCnxPXonb9

endpoints:
  - name: cognito
    upstream:
      url: http://localhost:9229
    traffic_policy:
      on_http_request:
        - actions:
            - type: add-headers
              config:
                headers:
                  Access-Control-Allow-Origin: "*"
  - name: api
    upstream:
      url: http://localhost:8000
    traffic_policy:
      on_http_request:
        - actions:
            - type: add-headers
              config:
                headers:
                  Access-Control-Allow-Origin: "*"
