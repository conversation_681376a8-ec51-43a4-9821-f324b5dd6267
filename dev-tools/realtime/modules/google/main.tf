resource "random_id" "this" {
  byte_length = 8
}

locals {
  project_id = "${var.project_id}-${random_id.this.hex}"
}

# Google Cloud platform project
resource "google_project" "this" {
  name       = "test-${random_id.this.hex}"
  project_id = local.project_id
  org_id     = var.organisation_id

  deletion_policy = "DELETE"

  labels = {
    "firebase" = "enabled"
  }
}

# Firebase platform project and resources
resource "google_firebase_project" "this" {
  provider = google-beta

  project = google_project.this.project_id
}

resource "google_firebase_android_app" "this" {
  provider = google-beta

  project = google_firebase_project.this.project

  display_name = "Aerobid"
  package_name = "com.aerobid.mobile"

  # Service account creation is eventually consistent, so add a delay.
  # provisioner "local-exec" {
  #   command = "sleep 10"
  # }
}

data "google_firebase_android_app_config" "this" {
  provider = google-beta

  app_id  = google_firebase_android_app.this.app_id
  project = google_firebase_project.this.project

  depends_on = [google_firebase_android_app.this]
}

data "google_service_accounts" "this" {
  project = google_firebase_project.this.project

  regex      = "firebase-adminsdk.*"
  depends_on = [google_firebase_android_app.this]
}

# Other Google Cloud platform resources
# data "google_service_account" "this" {
#   provider = google-beta
#
#   account_id = "firebase-adminsdk-fbsvc@${local.project_id}.iam.gserviceaccount.com"
#   project    = google_project.this.id
#
#   depends_on = [google_firebase_project.this]
# }
#
# resource "google_service_account_key" "this" {
#   provider = google-beta
#
#   service_account_id = data.google_service_account.this.name
# }
