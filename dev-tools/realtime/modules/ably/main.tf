terraform {
  required_providers {
    ably = {
      source  = "ably/ably"
      version = "0.10.0"
    }
  }
}
data "local_file" "apns_private_key" {
  filename = "${path.root}/environments/${var.environment}/apple_push.key.pem"
}

data "local_file" "apns_certificate" {
  filename = "${path.root}/environments/${var.environment}/apple_push.crt.pem"
}

data "local_file" "fcm_key" {
  filename = "${path.root}/environments/${var.environment}/fcm_key"
}

resource "ably_app" "_app" {
  name                      = "aventur-test"
  status                    = "enabled"
  tls_only                  = true
  fcm_key                   = data.local_file.fcm_key.content
  apns_certificate          = data.local_file.apns_certificate.content
  apns_private_key          = data.local_file.apns_private_key.content
  apns_use_sandbox_endpoint = true
}

resource "ably_namespace" "private" {
  app_id            = ably_app._app.id
  id                = "private"
  authenticated     = true
  persisted         = true
  persist_last      = false
  push_enabled      = true
  tls_only          = true
  expose_timeserial = false
}

resource "ably_api_key" "_api_key" {
  app_id = ably_app._app.id
  name   = "aventur-test"
  capabilities = {
    "[*]*": ["history","presence","publish","push-admin","push-subscribe","subscribe"]
  }
}
