### Prerequisites

#### Google Cloud

1. Create an organisation Google Cloud account: https://cloud.google.com/
2. From the service account key page in the Cloud Console choose an existing account, or create a new one. Next, download the JSON key file. Name it something you can remember, and store it in this directory. 
3. Enable the following APIs:
   - Cloud Resource Manager API
   - Firebase Management API
   - Firebase Cloud Messaging API
4. Supply the key to Terraform using the environment variable `GOOGLE_APPLICATION_CREDENTIALS`, setting the value to the location of the file:

```bash
export GOOGLE_APPLICATION_CREDENTIALS=/path/to/your/service-account-file.json
```


#### Apple Developer

1. Create Apple Developer account: https://developer.apple.com/
2. Create a new certificate for push notifications: https://developer.apple.com/documentation/usernotifications/setting_up_a_remote_notification_server/establishing_a_certificate-based_connection_to_apns
3. Export the certificate as a p12 file and store it in this directory. Name it `apple_push_dev.p12`.


### Ably

1. Create Ably account: https://ably.com/
2. Create a Control API token and store it in the `ably_api_key` file in this folder.
3. Run `terraform init` to install the Ably provider:
```bash
terraform init \
-backend-config="bucket=aventur-terraform" \
-backend-config="key=aventur.tfstate"
```

4. Run `terraform apply` to create the app, keys and namespace:
```bash
terraform plan \
-var-file="./environments/dev/terraform.tfvars"
```

