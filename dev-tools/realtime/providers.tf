provider "aws" {
  profile = var.aws_profile
  region  = var.aws_region

  default_tags {
    tags = {
      ManagedBy   = "terraform",
      Project     = var.project
      Environment = var.environment
    }
  }
}

provider "ably" {
  token = var.ably_token
}

provider "google-beta" {
  project     = var.google_project_id
  
  region      = "europe-west2"
  zone        = "europe-west2-a"

  user_project_override = false
}
