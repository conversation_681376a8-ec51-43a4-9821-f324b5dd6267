module "google" {
  source = "./modules/google"

  project_id = var.google_project_id
  organisation_id = var.google_organisation_id
  fcm_project_id = var.fcm_project_id
}

# resource "local_file" "google_android_app_config" {
#   content  = module.google.android_app_config
#   filename = "google-services.json"
# }

# resource "local_file" "google_service_account_key" {
#   content  = base64decode(module.google.service_account_key)
#   filename = "google-sa-key.json"
# }

# module "ably" {
#   source = "./modules/ably"
#
#   environment = var.environment
#   project = var.project
#   ably_token = var.ably_token
#
#   depends_on = [module.google]
# }
