variable "aws_profile" {
  type    = string
  default = "aventur"
}

variable "aws_region" {
  type    = string
  default = "eu-west-2"
}

variable "project" {
  type        = string
  description = "Project identifier"
}

variable "environment" {
  type        = string
  description = "Deployment environment name"
}

variable "ably_token" {
  type        = string
  description = "Ably token"
}

variable "google_project_id" {
  type        = string
  description = "Google project identifier"
}

variable "google_organisation_id" {
  type        = string
  description = "Google organisation identifier"
}

variable "fcm_project_id" {
  type        = string
  description = "Firebase Cloud Messaging project identifier"
}

variable "tags" {
  type    = map(string)
  default = {}
}
