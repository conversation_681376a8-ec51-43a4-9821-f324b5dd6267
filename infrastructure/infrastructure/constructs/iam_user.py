from aws_cdk import (
    aws_iam as iam,
)
from aws_cdk import (
    aws_ssm as ssm,
)
from constructs import Construct
from infrastructure.lambda_functions.lambda_function import AventurLambdaConstruct
from infrastructure.s3_stack import AventurBucketStack


class AventurIAMUserConstruct(Construct):
    def __init__(
        self,
        scope: Construct,
        id: str,
        env_name: str,
        user_pool: str,
        fifo_queue: str,
        document_bucket: AventurBucketStack,
        lambda_function: AventurLambdaConstruct,
        **kwargs,
    ) -> None:
        super().__init__(scope, id)

        ts_db_name = ssm.StringParameter.value_for_string_parameter(
            self, f"/aventur-jarvis/{env_name}/TS_DATABASE_NAME"
        )

        user_name = f"jarvis-{env_name}"

        self.user = iam.User(self, "User", user_name=user_name)

        self.cfn_access_key = iam.CfnAccessKey(
            self,
            "AccessKey",
            user_name=self.user.user_name,
        )

        cognito_policy = iam.Policy(
            self,
            "CognitoPolicy",
            statements=[
                iam.PolicyStatement(
                    actions=[
                        "cognito-idp:AdminGetUser",
                        "cognito-idp:AdminSetUserPassword",
                        "cognito-idp:AdminListGroupsForUser",
                        "cognito-idp:AdminEnableUser",
                        "cognito-idp:ListUsers",
                        "cognito-idp:AdminDisableUser",
                        "cognito-idp:ListGroups",
                        "cognito-idp:AdminCreateUser",
                        "cognito-idp:AdminAddUserToGroup",
                        "cognito-idp:AdminRemoveUserFromGroup",
                        "cognito-idp:AdminUpdateUserAttributes",
                    ],
                    resources=[
                        f"arn:aws:cognito-idp:eu-west-2:919426555311:userpool/{user_pool}"
                    ],
                )
            ],
        )

        self.user.attach_inline_policy(cognito_policy)

        timestream_policy = iam.Policy(
            self,
            "TimestreamPolicy",
            statements=[
                iam.PolicyStatement(
                    actions=[
                        "timestream:WriteRecords",
                        "timestream:Select",
                        "timestream:ListTables",
                        "timestream:ListDatabases",
                    ],
                    resources=[
                        f"arn:aws:timestream:eu-west-1:919426555311:database/{ts_db_name}",
                        f"arn:aws:timestream:eu-west-1:919426555311:database/{ts_db_name}/table/*",
                    ],
                ),
                iam.PolicyStatement(
                    actions=[
                        "timestream:DescribeEndpoints",
                    ],
                    resources=["*"],
                ),
            ],
        )

        self.user.attach_inline_policy(timestream_policy)

        sqs_policy = iam.Policy(
            self,
            "SQSPolicy",
            statements=[
                iam.PolicyStatement(
                    actions=[
                        "sqs:*",
                    ],
                    resources=[f"arn:aws:sqs:eu-west-2:919426555311:{fifo_queue}"],
                )
            ],
        )

        self.user.attach_inline_policy(sqs_policy)

        bucket_policy = iam.Policy(
            self,
            "S3BucketPolicy",
            statements=[
                document_bucket.document_bucket_policy,
                document_bucket.document_policy,
            ],
        )

        self.user.attach_inline_policy(bucket_policy)

        lamdba_assume_role_policy = iam.Policy(
            self,
            "LambdaPolicy",
            statements=[
                iam.PolicyStatement(
                    actions=[
                        "sts:AssumeRole",
                    ],
                    resources=[lambda_function.lambda_role.role_arn],
                )
            ],
        )

        self.user.attach_inline_policy(lamdba_assume_role_policy)

        bedrock_policy = iam.Policy(
            self,
            "BedrockPolicy",
            statements=[
                iam.PolicyStatement(
                    effect=iam.Effect.ALLOW,
                    actions=[
                        "bedrock:InvokeModelWithResponseStream",
                        "bedrock:InvokeModel",
                    ],
                    resources=[
                        "arn:aws:bedrock:eu-west-1:919426555311:inference-profile/eu.anthropic.claude-*",
                        "arn:aws:bedrock:eu-*::foundation-model/anthropic.claude-*",
                    ],
                )
            ],
        )

        self.user.attach_inline_policy(bedrock_policy)

        # Note: I don't think these are required but I'm leaving the code here just incase

        # ses_policy = iam.Policy(
        #     self,
        #     "SESPolicy",
        #     statements=[
        #         iam.PolicyStatement(
        #             actions=[
        #                 "ses:SendEmail",
        #             ],
        #             resources=["*"],
        #         )
        #     ],
        # )
        #
        # self.user.attach_inline_policy(ses_policy)

        # logging_policy = iam.Policy(
        #     self,
        #     "LoggingPolicy",
        #     statements=[
        #         iam.PolicyStatement(
        #             actions=[
        #                 "logs:CreateLogStream",
        #                 "logs:PutLogEvents",
        #                 "logs:DescribeLogGroups",
        #                 "logs:DescribeLogStreams",
        #             ],
        #             resources=["*"],
        #         )
        #     ],
        # )
        #
        # self.user.attach_inline_policy(logging_policy)
